<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Interactive Chart - Evaluation 66</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-item {
            transition: all 0.3s ease;
        }
        .test-item:hover {
            transform: translateX(5px);
        }
        .test-passed {
            background-color: #dcfce7;
            border-color: #16a34a;
        }
        .test-failed {
            background-color: #fef2f2;
            border-color: #dc2626;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-6xl mx-auto px-4">
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-blue-600 mb-4">
                <i class="fas fa-chart-bar mr-3"></i>
                Test Interactive Chart - Evaluation 66
            </h1>
            <p class="text-gray-600 text-lg">Vérification complète des fonctionnalités du graphique interactif</p>
        </div>

        <!-- Quick Access -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">Accès Rapide</h2>
            <div class="flex flex-wrap gap-4">
                <a href="http://127.0.0.1:8000/cobit/evaluation/66/canvas" 
                   target="_blank"
                   class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center space-x-2">
                    <i class="fas fa-external-link-alt"></i>
                    <span>Ouvrir Evaluation 66</span>
                </a>
                <a href="http://127.0.0.1:8000/cobit/evaluation/5/canvas" 
                   target="_blank"
                   class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors inline-flex items-center space-x-2">
                    <i class="fas fa-external-link-alt"></i>
                    <span>Test BIAT (ID: 5)</span>
                </a>
                <a href="http://127.0.0.1:8000/cobit/evaluation/11/canvas" 
                   target="_blank"
                   class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors inline-flex items-center space-x-2">
                    <i class="fas fa-external-link-alt"></i>
                    <span>Test KPMG (ID: 11)</span>
                </a>
            </div>
        </div>

        <!-- Test Checklist -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Functional Tests -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-2xl font-bold mb-6 text-gray-800 flex items-center">
                    <i class="fas fa-cogs mr-3 text-blue-600"></i>
                    Tests Fonctionnels
                </h2>
                <div class="space-y-4">
                    <div class="test-item border-2 border-gray-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" id="test1" class="w-5 h-5 text-blue-600">
                            <label for="test1" class="font-medium">Boutons de filtrage présents et visibles</label>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">Vérifier que les boutons Top 3, Top 5, Top 10, et Tous sont affichés</p>
                    </div>
                    
                    <div class="test-item border-2 border-gray-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" id="test2" class="w-5 h-5 text-blue-600">
                            <label for="test2" class="font-medium">Filtrage Top 3 fonctionne</label>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">Cliquer sur "Top 3" doit afficher exactement 3 barres</p>
                    </div>
                    
                    <div class="test-item border-2 border-gray-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" id="test3" class="w-5 h-5 text-blue-600">
                            <label for="test3" class="font-medium">Filtrage Top 5 fonctionne</label>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">Cliquer sur "Top 5" doit afficher exactement 5 barres</p>
                    </div>
                    
                    <div class="test-item border-2 border-gray-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" id="test4" class="w-5 h-5 text-blue-600">
                            <label for="test4" class="font-medium">Filtrage Top 10 fonctionne</label>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">Cliquer sur "Top 10" doit afficher exactement 10 barres</p>
                    </div>
                    
                    <div class="test-item border-2 border-gray-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" id="test5" class="w-5 h-5 text-blue-600">
                            <label for="test5" class="font-medium">Filtrage "Tous" fonctionne</label>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">Cliquer sur "Tous" doit afficher tous les objectifs disponibles</p>
                    </div>
                    
                    <div class="test-item border-2 border-gray-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" id="test6" class="w-5 h-5 text-blue-600">
                            <label for="test6" class="font-medium">Compteur se met à jour</label>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">Le nombre d'objectifs affiché doit changer selon le filtre</p>
                    </div>
                </div>
            </div>

            <!-- Visual Tests -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-2xl font-bold mb-6 text-gray-800 flex items-center">
                    <i class="fas fa-palette mr-3 text-purple-600"></i>
                    Tests Visuels
                </h2>
                <div class="space-y-4">
                    <div class="test-item border-2 border-gray-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" id="test7" class="w-5 h-5 text-purple-600">
                            <label for="test7" class="font-medium">Barres colorées différemment</label>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">Chaque barre doit avoir une couleur différente</p>
                    </div>
                    
                    <div class="test-item border-2 border-gray-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" id="test8" class="w-5 h-5 text-purple-600">
                            <label for="test8" class="font-medium">États actifs des boutons</label>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">Le bouton actif doit être visuellement distinct (bleu foncé)</p>
                    </div>
                    
                    <div class="test-item border-2 border-gray-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" id="test9" class="w-5 h-5 text-purple-600">
                            <label for="test9" class="font-medium">Animations fluides</label>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">Transitions animées lors du changement de filtre</p>
                    </div>
                    
                    <div class="test-item border-2 border-gray-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" id="test10" class="w-5 h-5 text-purple-600">
                            <label for="test10" class="font-medium">Notifications apparaissent</label>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">Notification en haut à droite lors du filtrage</p>
                    </div>
                    
                    <div class="test-item border-2 border-gray-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" id="test11" class="w-5 h-5 text-purple-600">
                            <label for="test11" class="font-medium">Tooltips informatifs</label>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">Survol des barres affiche score, priorité, gap</p>
                    </div>
                    
                    <div class="test-item border-2 border-gray-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" id="test12" class="w-5 h-5 text-purple-600">
                            <label for="test12" class="font-medium">Modal de détails</label>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">Clic sur barre ouvre modal avec détails complets</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl shadow-lg p-6 mt-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">Instructions de Test</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-bold text-lg mb-3 text-blue-700">Étapes de Test:</h3>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>Ouvrir l'évaluation 66 via le lien ci-dessus</li>
                        <li>Localiser la section "Performance par Objectif"</li>
                        <li>Tester chaque bouton de filtrage (Top 3, 5, 10, Tous)</li>
                        <li>Vérifier le nombre de barres affichées</li>
                        <li>Observer les animations et transitions</li>
                        <li>Tester l'interactivité (hover, clic)</li>
                        <li>Cocher les tests réussis</li>
                    </ol>
                </div>
                <div>
                    <h3 class="font-bold text-lg mb-3 text-purple-700">Points d'Attention:</h3>
                    <ul class="list-disc list-inside space-y-2 text-gray-700">
                        <li>Les objectifs doivent être triés par score décroissant</li>
                        <li>Le compteur doit refléter le nombre exact d'objectifs</li>
                        <li>Les couleurs doivent être distinctes et attrayantes</li>
                        <li>Les notifications doivent être temporaires (2.5s)</li>
                        <li>La modal doit être responsive et bien formatée</li>
                        <li>Tous les éléments doivent être accessibles</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Progress Tracker -->
        <div class="bg-white rounded-xl shadow-lg p-6 mt-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">Progression des Tests</h2>
            <div class="flex items-center space-x-4">
                <div class="flex-1 bg-gray-200 rounded-full h-4">
                    <div id="progress-bar" class="bg-gradient-to-r from-blue-500 to-purple-600 h-4 rounded-full transition-all duration-500" style="width: 0%"></div>
                </div>
                <span id="progress-text" class="font-bold text-gray-700">0/12 tests</span>
            </div>
        </div>
    </div>

    <script>
        // Progress tracking
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        
        function updateProgress() {
            const checked = document.querySelectorAll('input[type="checkbox"]:checked').length;
            const total = checkboxes.length;
            const percentage = (checked / total) * 100;
            
            progressBar.style.width = percentage + '%';
            progressText.textContent = `${checked}/${total} tests`;
            
            // Update test item styling
            checkboxes.forEach(checkbox => {
                const testItem = checkbox.closest('.test-item');
                if (checkbox.checked) {
                    testItem.classList.add('test-passed');
                    testItem.classList.remove('test-failed');
                } else {
                    testItem.classList.remove('test-passed', 'test-failed');
                }
            });
        }
        
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateProgress);
        });
        
        // Initialize
        updateProgress();
    </script>
</body>
</html>
