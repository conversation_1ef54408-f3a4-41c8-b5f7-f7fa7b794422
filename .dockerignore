# Fichiers et dossiers à ignorer lors de la construction Docker

# Dossiers de dépendances
node_modules/
vendor/

# Fichiers de configuration locale
.env
.env.local
.env.*.local

# Logs
storage/logs/
*.log

# Cache
storage/framework/cache/
storage/framework/sessions/
storage/framework/views/
bootstrap/cache/

# Fichiers temporaires
.tmp/
.temp/

# Fichiers de développement
.git/
.gitignore
README.md
*.md

# Fichiers IDE
.vscode/
.idea/
*.swp
*.swo

# Fichiers OS
.DS_Store
Thumbs.db

# Tests
tests/
phpunit.xml
.phpunit.result.cache

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Autres
.editorconfig
.styleci.yml
