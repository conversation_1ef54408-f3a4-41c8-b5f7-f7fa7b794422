# 🏢 PLATEFORME COBIT 2019 - ÉVALUATION DE GOUVERNANCE IT

<p align="center">
  <img src="https://img.shields.io/badge/Laravel-10-red?style=for-the-badge&logo=laravel" alt="Laravel 10">
  <img src="https://img.shields.io/badge/PHP-8.1+-blue?style=for-the-badge&logo=php" alt="PHP 8.1+">
  <img src="https://img.shields.io/badge/COBIT-2019-green?style=for-the-badge" alt="COBIT 2019">
  <img src="https://img.shields.io/badge/AI-Powered-purple?style=for-the-badge&logo=robot" alt="AI Powered">
</p>

## 📋 DESCRIPTION

Plateforme web complète d'évaluation de la gouvernance IT basée sur le référentiel **COBIT 2019**. Cette solution permet aux entreprises d'évaluer leur maturité en gouvernance IT à travers les 10 Design Factors et de générer des rapports détaillés avec recommandations.

## 🎯 FONCTIONNALITÉS PRINCIPALES

### 🔍 **Évaluation COBIT 2019**
- ✅ **10 Design Factors** : Évaluation complète selon le référentiel officiel
- ✅ **40 Objectifs de gouvernance** : Scoring détaillé par objectif
- ✅ **Calcul de maturité** : Niveau global et par domaine
- ✅ **Recommandations personnalisées** : Basées sur les résultats

### 🤖 **Agent IA Intégré**
- ✅ **Analyse automatique** : Upload et traitement de documents PDF/Excel
- ✅ **Pré-remplissage intelligent** : Paramètres suggérés selon le contenu
- ✅ **Reconnaissance COBIT** : Mapping automatique vers les Design Factors
- ✅ **Gain de temps** : Réduction significative du temps d'évaluation

### 📊 **Rapports et Visualisations**
- ✅ **Canvas interactif** : Visualisation graphique des résultats
- ✅ **Graphiques radar** : Comparaison par domaine
- ✅ **Tableaux de bord** : Métriques et KPI
- ✅ **Export PDF** : Rapports professionnels

### 👥 **Gestion Multi-Utilisateurs**
- ✅ **Authentification** : Système de connexion sécurisé
- ✅ **Profils utilisateurs** : Gestion des droits et permissions
- ✅ **Historique** : Suivi des évaluations précédentes
- ✅ **Collaboration** : Partage et commentaires

## 🛠️ TECHNOLOGIES

- **Backend** : Laravel 10, PHP 8.1+, MySQL
- **Frontend** : Blade Templates, Tailwind CSS, JavaScript ES6
- **IA** : Agent d'analyse de documents intégré
- **Visualisation** : Chart.js, Canvas interactif
- **Sécurité** : Authentification Laravel, CSRF Protection

## 🚀 INSTALLATION RAPIDE

```bash
# 1. Cloner le projet
git clone [URL_DU_REPO]
cd cobit-laravel

# 2. Installer les dépendances
composer install
npm install && npm run build

# 3. Configuration
cp .env.example .env
php artisan key:generate

# 4. Base de données
php artisan migrate
php artisan db:seed

# 5. Démarrer
php artisan serve
```

Accédez à `http://localhost:8000` pour commencer !

## 📖 GUIDE D'UTILISATION

### 1. **Commencer une Évaluation**
- Cliquez sur "Commencer l'évaluation"
- Renseignez les informations de votre entreprise
- Optionnel : Utilisez l'Agent IA pour pré-remplir

### 2. **Agent IA** 🤖
- Uploadez vos documents (PDF/Excel)
- L'IA analyse et pré-remplit les Design Factors
- Modifiez les valeurs si nécessaire

### 3. **Évaluation**
- Parcourez les 10 Design Factors
- Évaluez chaque objectif (1-5)
- Sauvegardez vos progrès

### 4. **Résultats**
- Visualisez sur le canvas interactif
- Consultez les recommandations
- Exportez le rapport PDF

## 🎯 DESIGN FACTORS COBIT 2019

| DF | Nom | Description |
|----|-----|-------------|
| DF1 | Enterprise Strategy | Stratégie et objectifs d'entreprise |
| DF2 | Enterprise Goals | Objectifs et métriques d'entreprise |
| DF3 | Risk Profile | Profil et appétence au risque |
| DF4 | I&T-Related Issues | Questions et enjeux IT |
| DF5 | Threat Landscape | Paysage des menaces |
| DF6 | Compliance Requirements | Exigences de conformité |
| DF7 | Role of IT | Rôle et positionnement de l'IT |
| DF8 | Sourcing Model | Modèle d'approvisionnement |
| DF9 | IT Implementation Methods | Méthodes d'implémentation IT |
| DF10 | Enterprise Size | Taille et complexité de l'entreprise |

## 📁 STRUCTURE

```
cobit-laravel/
├── app/
│   ├── Http/Controllers/
│   │   ├── CobitController.php      # Logique métier COBIT
│   │   └── AuthController.php       # Authentification
│   ├── Models/
│   │   ├── Evaluation.php           # Modèle d'évaluation
│   │   └── User.php                 # Modèle utilisateur
│   └── Services/
│       └── CobitAnalysisService.php # Service d'analyse IA
├── resources/views/cobit/           # Vues COBIT
├── database/migrations/             # Migrations
└── routes/web.php                   # Routes
```

## 🔒 SÉCURITÉ

- ✅ **Authentification** Laravel sécurisée
- ✅ **CSRF Protection** activée
- ✅ **Validation** stricte des entrées
- ✅ **Upload sécurisé** avec validation
- ✅ **Nettoyage automatique** des fichiers temporaires

## 📈 PERFORMANCE

- ✅ **Cache** des résultats de calcul
- ✅ **Optimisation** des requêtes base de données
- ✅ **Compression** des assets CSS/JS
- ✅ **Analyse IA rapide** (< 10 secondes)

## 🧪 TESTS

```bash
# Tests unitaires
php artisan test

# Tests d'intégration
php artisan test --testsuite=Feature
```

## 🤝 CONTRIBUTION

1. Forkez le projet
2. Créez une branche feature
3. Committez vos changements
4. Créez une Pull Request

## 📞 SUPPORT

- **Issues** : Utilisez le système d'issues GitHub
- **COBIT 2019** : [Site officiel ISACA](https://www.isaca.org/resources/cobit)
- **Laravel** : [Documentation Laravel](https://laravel.com/docs)

## 📄 LICENCE

Ce projet est sous licence MIT.

---

**🚀 Évaluez et améliorez votre gouvernance IT avec COBIT 2019 !**
